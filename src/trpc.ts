import * as v from "valibot";

import {
	create<PERSON>ike<PERSON><PERSON><PERSON>,
	getAll<PERSON><PERSON><PERSON><PERSON><PERSON>,
	getLikedJokesByUser,
	unlike<PERSON><PERSON>,
} from "~/serverFn/likesServerFn";
import { likeJokeSchema } from "~/validation/schema";
import type { LikeJokeInput } from "~/validation/types";

// Define a type-safe API
// Since we don't have @trpc/server installed, we'll create a simple implementation
// that works with the existing serverFn functions

type Procedure<TInput, TOutput> = {
	input: (schema: any) => Procedure<TInput, TOutput>;
	query: (handler: (opts: { input: TInput }) => Promise<TOutput>) => any;
	mutation: (handler: (opts: { input: TInput }) => Promise<TOutput>) => any;
};

type Router = {
	procedure: Procedure<any, any>;
	router: (routes: Record<string, any>) => any;
};

// Simple mock of tRPC functionality to work with existing serverFn
const t = {
	router: (routes: Record<string, any>) => routes,
	procedure: {
		input: (schema: any) => ({
			query: (handler: (opts: { input: any }) => Promise<any>) => ({
				handler,
				schema,
			}),
			mutation: (handler: (opts: { input: any }) => Promise<any>) => ({
				handler,
				schema,
			}),
		}),
	},
} as Router;

// Create likes router
export const likesRouter = t.router({
	getLikedByUser: t.procedure
		.input(v.string())
		.query(async ({ input }: { input: string }) => {
			return await getLikedJokesByUser({ data: input });
		}),

	getAllLikes: t.procedure
		.input(v.string())
		.query(async ({ input }: { input: string }) => {
			return await getAllJokeLikes({ data: input });
		}),

	likeJoke: t.procedure
		.input(likeJokeSchema)
		.mutation(async ({ input }: { input: LikeJokeInput }) => {
			return await createLikedJoke({ data: input });
		}),

	unlikeJoke: t.procedure
		.input(likeJokeSchema)
		.mutation(async ({ input }: { input: LikeJokeInput }) => {
			return await unlikeJoke({ data: input });
		}),
});

// Create root router
export const appRouter = t.router({
	likes: likesRouter,
});

// Export type router type signature
export type AppRouter = typeof appRouter;

// Export utility functions to use the API
export const likesApi = {
	getLikedByUser: async (userId: string) => {
		return await getLikedJokesByUser({ data: userId });
	},
	getAllLikes: async (jokeId: string) => {
		return await getAllJokeLikes({ data: jokeId });
	},
	likeJoke: async (data: LikeJokeInput) => {
		return await createLikedJoke({ data });
	},
	unlikeJoke: async (data: LikeJokeInput) => {
		return await unlikeJoke({ data });
	},
};
