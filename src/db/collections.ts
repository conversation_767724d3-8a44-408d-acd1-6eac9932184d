import { queryCollectionOptions } from "@tanstack/query-db-collection";
import { createCollection } from "@tanstack/react-db";
import { queryClient } from "~/lib/queryClient";
import type { LikedJokeSelect } from "~/validation/types";

export const likedJokesCollection = createCollection(
	queryCollectionOptions({
		queryClient,
		queryKey: ["likedJokes"],
		queryFn: async () => {
			const response = await fetch("/api/joke/get-joke-by-user");

			const likedJokes: LikedJokeSelect[] = await response.json();

			return likedJokes || [];
		},
		getKey: (item) => item.id,
		/*onInsert: async (item) => {
			const response = await fetch("/api/joke/like-joke", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(item),
			});

			return await response.json();
		},*/
	}),
);
