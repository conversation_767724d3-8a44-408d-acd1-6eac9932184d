import { queryCollectionOptions } from "@tanstack/query-db-collection";
import { createCollection } from "@tanstack/react-db";
import { queryClient } from "~/lib/queryClient";
import { getLikedJokes, toggleLikeJoke } from "~/serverFn/likesServerFn";

export const likedJokesCollection = createCollection(
	queryCollectionOptions({
		queryClient,
		queryKey: ["likedJokes"],
		queryFn: async () => {
			const { likedJokes } = await getLikedJokes();

			return likedJokes || [];
		},
		getKey: (item) => item.id,
		onUpdate: async ({ transaction }) => {
			console.log(1);
			const { modified } = transaction.mutations[0];

			await toggleLikeJoke({ data: modified });
		},
	}),
);
