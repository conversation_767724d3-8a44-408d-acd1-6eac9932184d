import { createServerFileRoute } from "@tanstack/react-start/server";
import { getLikedJokesByUser } from "~/serverFn/likesServerFn";

export const ServerRoute = createServerFileRoute("/api/joke/get-joke-by-user")
	.middlewares([getSession])
	.methods({
		GET: async ({ request }) => {
			const userId = await request.json();
			try {
				return await getLikedJokesByUser(userId);
			} catch (error) {
				console.error("Failed to get likes count:", error);
				return [];
			}
		},
	});
